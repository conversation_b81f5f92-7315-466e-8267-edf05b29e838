import os
import time
import pandas as pd
import re
from extractors import (
    extract_acta,
    extract_nss,
    extract_aviso_retencion,
    extract_comprobante_domicilio,
    extract_sat,
    extract_ine,
    extract_cuenta
)

"""
Módulo de orquestación de extracción semántica.
- Llama a los extractores específicos por tipo de documento.
- Implementa lógica de reintentos y manejo de rate limit.
- Ensambla y vincula los resultados por colaborador usando identificadores robustos (CURP, RFC, NSS, nombre, domicilio).
"""

def extraer_identificador(campos):
    """
    Determina el identificador principal del colaborador a partir de los campos extraídos.
    Prioridad: CURP > RFC > Nombre completo > NSS
    Args:
        campos (dict): Diccionario de campos extraídos.
    Returns:
        str: Identificador único (CURP, RFC, nombre o NSS)
    """
    if "CURP" in campos and campos["CURP"]:
        return campos["CURP"]
    if "RFC" in campos and campos["RFC"]:
        return campos["RFC"]
    if all(k in campos for k in ["Nombre(s)", "Apellido_Paterno", "Apellido_Materno"]):
        nombre = f"{campos['Nombre(s)']} {campos['Apellido_Paterno']} {campos['Apellido_Materno']}".strip()
        if nombre:
            return nombre
    if "NSS" in campos and campos["NSS"]:
        return campos["NSS"]
    return ""

def extraer_id_colaborador(campos):
    """
    Extrae el identificador del colaborador a partir de los campos extraídos.
    Utiliza la misma lógica que extraer_identificador.
    Args:
        campos (dict): Diccionario de campos extraídos.
    Returns:
        str: Identificador único (CURP, RFC, nombre o NSS)
    """
    return extraer_identificador(campos)

def llamar_extractor_con_reintentos(extractor, texto_ocr, api_key, max_reintentos=3, espera=10):
    """
    Llama a un extractor con reintentos automáticos en caso de rate limit.
    Args:
        extractor (func): Función extractor.
        texto_ocr (str): Texto extraído por OCR.
        api_key (str): API Key de Mistral.
        max_reintentos (int): Número máximo de reintentos.
        espera (int): Segundos de espera entre reintentos.
    Returns:
        dict: Campos extraídos.
    """
    for intento in range(max_reintentos):
        try:
            return extractor(texto_ocr, api_key)
        except Exception as e:
            if "429" in str(e) or "rate limit" in str(e).lower():
                print(f"⚠️ Rate limit alcanzado. Esperando {espera} segundos antes de reintentar (intento {intento+1}/{max_reintentos})...")
                time.sleep(espera)
            else:
                print(f"❌ Error inesperado en extractor: {e}")
                break
    print("❌ No se pudo procesar el documento tras varios intentos.")
    return {}

def extraer_campos_llm(texto_ocr, tipo_documento, api_key=None, max_reintentos=2):
    """
    Llama al extractor adecuado según el tipo de documento y retorna los campos extraídos y el identificador.
    Args:
        texto_ocr (str): Texto extraído por OCR.
        tipo_documento (str): Tipo de documento detectado.
        api_key (str, opcional): API Key de Mistral. Si no se pasa, se carga del entorno.
        max_reintentos (int): Número máximo de reintentos (reducido para mayor velocidad).
    Returns:
        dict: {"id_colaborador": str, "campos": dict}
    """
    if api_key is None:
        api_key = os.environ.get("MISTRAL_API_KEY")
    
    # Limitar el texto para reducir tokens y tiempo de procesamiento
    texto_ocr_limitado = texto_ocr[:4000]  # Limitar a 4000 caracteres
    
    # Extraer información básica con expresiones regulares antes de usar LLM
    campos_pre = extraer_campos_con_regex(texto_ocr_limitado, tipo_documento)
    
    # Si se encontraron suficientes campos con regex, no usar LLM
    if campos_pre and len(campos_pre) >= 3:
        return {"id_colaborador": extraer_id_colaborador(campos_pre), "campos": campos_pre}
    
    # Si no hay suficientes campos, usar LLM
    try:
        if tipo_documento == "acta":
            campos = llamar_extractor_con_reintentos(extract_acta, texto_ocr_limitado, api_key, max_reintentos)
        elif tipo_documento == "nss":
            campos = llamar_extractor_con_reintentos(extract_nss, texto_ocr_limitado, api_key, max_reintentos)
        elif tipo_documento == "aviso_retencion":
            campos = llamar_extractor_con_reintentos(extract_aviso_retencion, texto_ocr_limitado, api_key, max_reintentos)
        elif tipo_documento == "comprobante_domicilio":
            campos = llamar_extractor_con_reintentos(extract_comprobante_domicilio, texto_ocr_limitado, api_key, max_reintentos)
        elif tipo_documento == "sat":
            campos = llamar_extractor_con_reintentos(extract_sat, texto_ocr_limitado, api_key, max_reintentos)
        elif tipo_documento == "ine":
            campos = llamar_extractor_con_reintentos(extract_ine, texto_ocr_limitado, api_key, max_reintentos)
        elif tipo_documento == "cuenta":
            campos = llamar_extractor_con_reintentos(extract_cuenta, texto_ocr_limitado, api_key, max_reintentos)
        else:
            campos = {}
        
        # Combinar campos encontrados por regex con los del LLM
        if campos_pre:
            for k, v in campos_pre.items():
                if k not in campos or not campos[k]:
                    campos[k] = v
        
        return {"id_colaborador": extraer_id_colaborador(campos), "campos": campos}
    except Exception as e:
        print(f"❌ Error en extracción LLM: {e}")
        return {"id_colaborador": "", "campos": campos_pre or {}}

def extraer_campos_con_regex(texto, tipo_documento):
    """
    Extrae campos comunes usando expresiones regulares para reducir
    la dependencia del LLM y mejorar la velocidad.
    """
    campos = {}
    
    # Buscar CURP (formato estándar mexicano)
    curp_matches = re.findall(r'[A-Z]{4}[0-9]{6}[HM][A-Z]{5}[0-9]{2}', texto)
    if curp_matches:
        campos["CURP"] = curp_matches[0]
    
    # Buscar RFC (formato estándar mexicano)
    rfc_matches = re.findall(r'[A-Z]{3,4}[0-9]{6}[A-Z0-9]{3}', texto)
    if rfc_matches:
        # Filtrar posibles CURPs que también coincidan con el patrón de RFC
        rfcs = [rfc for rfc in rfc_matches if rfc != campos.get("CURP", "")]
        if rfcs:
            campos["RFC"] = rfcs[0]
    
    # Buscar NSS (formato estándar mexicano)
    nss_matches = re.findall(r'\b\d{10,11}\b', texto)
    if nss_matches:
        campos["NSS"] = nss_matches[0]
    
    # Buscar códigos postales mexicanos
    cp_match = re.search(r'C\.?P\.?\s*:?\s*(\d{5})', texto)
    if cp_match:
        campos["C.P."] = cp_match.group(1)
    
    # Buscar nombres y apellidos (si es relevante para el tipo de documento)
    if tipo_documento in ["acta", "ine", "sat"]:
        # Patrones comunes para nombres
        nombre_match = re.search(r'NOMBRE[S]?[\s:]+([A-ZÁÉÍÓÚÑ\s]+)', texto)
        if nombre_match:
            campos["Nombre(s)"] = nombre_match.group(1).strip()
        
        # Patrones para apellidos
        ap_match = re.search(r'APELLIDO\s+PATERNO[\s:]+([A-ZÁÉÍÓÚÑ\s]+)', texto)
        if ap_match:
            campos["Apellido_Paterno"] = ap_match.group(1).strip()
        
        am_match = re.search(r'APELLIDO\s+MATERNO[\s:]+([A-ZÁÉÍÓÚÑ\s]+)', texto)
        if am_match:
            campos["Apellido_Materno"] = am_match.group(1).strip()
    
    # Buscar fecha de nacimiento
    if tipo_documento in ["acta", "ine"]:
        fecha_match = re.search(r'\b(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})\b', texto)
        if fecha_match:
            campos["Fecha_de_Nacimiento"] = fecha_match.group(1)
    
    return campos

# Ensamblador robusto: vincula documentos por CURP, RFC, NSS, nombre completo y domicilio
# Forza el match de comprobante de domicilio y cuenta bancaria por nombre o domicilio si no hay CURP

def ensamblar_por_colaborador(resultados):
    """
    Agrupa y vincula los resultados de extracción por colaborador usando identificadores robustos.
    Da prioridad a CURP, luego RFC, NSS, nombre y domicilio. Intenta vincular comprobante de domicilio y cuenta bancaria por nombre o domicilio si no hay identificador fuerte.
    Args:
        resultados (list): Lista de dicts con resultados de extracción.
    Returns:
        dict: Expedientes agrupados por colaborador.
    """
    # Índices auxiliares para vinculación secundaria
    por_curp = {}
    por_rfc = {}
    por_nss = {}
    por_nombre = {}
    por_domicilio = {}

    expedientes = {}
    pendientes = []  # Para documentos sin identificador fuerte

    for res in resultados:
        campos = res["campos"]
        tipo = res["tipo"]
        archivo = res["archivo"]
        curp = campos.get("CURP", "").strip().upper()
        rfc = campos.get("RFC", "").strip().upper()
        nss = campos.get("NSS", "").strip()
        nombre = (
            (campos.get("Nombre(s)", "") + " " +
             campos.get("Apellido_Paterno", "") + " " +
             campos.get("Apellido_Materno", "")).strip().upper()
        )
        domicilio = campos.get("Domicilio", "").strip().upper()

        # 1. Vinculación primaria por CURP
        id_col = None
        if curp:
            id_col = curp
        elif rfc:
            id_col = rfc
        elif nss:
            id_col = nss
        elif nombre and tipo != "comprobante_domicilio" and tipo != "cuenta":
            id_col = nombre
        else:
            # Para comprobante de domicilio y cuenta, intentar vincular después
            pendientes.append({"tipo": tipo, "archivo": archivo, "campos": campos, "nombre": nombre, "domicilio": domicilio})
            continue

        # Crea expediente si no existe
        if id_col not in expedientes:
            expedientes[id_col] = {}

        expedientes[id_col][tipo] = {
            "archivo": archivo,
            **campos
        }

        # Actualiza índices auxiliares
        if curp:
            por_curp[curp] = id_col
        if rfc:
            por_rfc[rfc] = id_col
        if nss:
            por_nss[nss] = id_col
        if nombre:
            por_nombre[nombre] = id_col
        if domicilio:
            por_domicilio[domicilio] = id_col

    # Intentar vincular comprobante de domicilio y cuenta bancaria por nombre o domicilio
    for doc in pendientes:
        tipo = doc["tipo"]
        archivo = doc["archivo"]
        campos = doc["campos"]
        nombre = doc["nombre"]
        domicilio = doc["domicilio"]
        id_col = None

        # 1. Buscar por nombre completo
        if nombre and nombre in por_nombre:
            id_col = por_nombre[nombre]
        # 2. Buscar por domicilio
        elif domicilio and domicilio in por_domicilio:
            id_col = por_domicilio[domicilio]
        # 3. Buscar por CURP en campos (por si acaso)
        elif campos.get("CURP", "").strip().upper() in por_curp:
            id_col = por_curp[campos.get("CURP", "").strip().upper()]
        # 4. Si no hay match, dejar como no_vinculado
        else:
            id_col = "no_vinculado"

        if id_col not in expedientes:
            expedientes[id_col] = {}
        expedientes[id_col][tipo] = {
            "archivo": archivo,
            **campos
        }

    return expedientes

def consolidar_empleados(expedientes, LAYOUT_CAMPOS=None, PRIORIDAD_CAMPOS=None, CAMPOS_DOMICILIO=None):
    """
    Consolida los datos de los expedientes agrupados en un DataFrame ancho, priorizando los campos según el tipo de documento.
    Realiza fusión de filas por nombre si hay duplicados sin CURP.
    Args:
        expedientes (dict): Expedientes agrupados por colaborador.
        LAYOUT_CAMPOS (list): Lista de campos objetivo.
        PRIORIDAD_CAMPOS (dict): Prioridad de tipos de documento por campo.
        CAMPOS_DOMICILIO (list): Lista de campos de domicilio.
    Returns:
        pd.DataFrame: DataFrame consolidado de empleados.
    """
    # Valores por defecto si no se pasan (para compatibilidad con main.py y app.py)
    if LAYOUT_CAMPOS is None:
        LAYOUT_CAMPOS = [
            "id_colaborador", "Apellido_Paterno", "Apellido_Materno", "Nombre(s)", "Fecha_de_Nacimiento", "CURP", "RFC", "NSS",
            "Domicilio", "Colonia", "C.P.", "Municipio", "Estado", "Numero_Credito_Infonavit", "Tipo_Descuento", "Factor_Descuento",
            "Banco", "Cuenta", "Cuenta_Clabe"
        ]
    if PRIORIDAD_CAMPOS is None:
        PRIORIDAD_CAMPOS = {
            "Domicilio": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "Colonia": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "C.P.": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "Municipio": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
            "Estado": ["comprobante_domicilio", "sat", "ine", "acta", "nss", "aviso_retencion"],
        }
    if CAMPOS_DOMICILIO is None:
        CAMPOS_DOMICILIO = ["Domicilio", "Colonia", "C.P.", "Municipio", "Estado"]

    empleados = []
    for id_col, docs in expedientes.items():
        fila = {campo: "" for campo in LAYOUT_CAMPOS}
        fila["id_colaborador"] = id_col
        for campo in LAYOUT_CAMPOS:
            prioridad = PRIORIDAD_CAMPOS.get(campo, docs.keys())
            for tipo in prioridad:
                if tipo in docs and campo in docs[tipo] and docs[tipo][campo]:
                    fila[campo] = docs[tipo][campo]
                    break  # Ya llené este campo con la mejor fuente
        empleados.append(fila)
    df = pd.DataFrame(empleados)

    # --- FUSIÓN POR NOMBRE EXACTO ---
    # Si una fila tiene CURP y otra solo nombre, y el nombre completo coincide, fusiona los datos de domicilio
    def nombre_completo(row):
        return f"{row['Nombre(s)']} {row['Apellido_Paterno']} {row['Apellido_Materno']}".strip().upper()

    # Índices para buscar coincidencias
    df_con_curp = df[df["CURP"] != ""].copy()
    df_sin_curp = df[(df["CURP"] == "") & (df["Nombre(s)"] != "")].copy()
    usados = set()
    for idx_sin, row_sin in df_sin_curp.iterrows():
        nombre_sin = nombre_completo(row_sin)
        for idx_con, row_con in df_con_curp.iterrows():
            nombre_con = nombre_completo(row_con)
            if nombre_sin == nombre_con:
                # Fusiona los campos de domicilio
                for campo in CAMPOS_DOMICILIO:
                    if row_sin[campo]:
                        df.at[idx_con, campo] = row_sin[campo]
                usados.add(idx_sin)
                break
    # Elimina las filas fusionadas (solo nombre)
    df = df.drop(list(usados)).reset_index(drop=True)
    return df 
