"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
import pydantic
from typing import List, Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


EmbeddingRequestInputsTypedDict = TypeAliasType(
    "EmbeddingRequestInputsTypedDict", Union[str, List[str]]
)
r"""Text to embed."""


EmbeddingRequestInputs = TypeAliasType("EmbeddingRequestInputs", Union[str, List[str]])
r"""Text to embed."""


class EmbeddingRequestTypedDict(TypedDict):
    model: str
    r"""ID of the model to use."""
    inputs: EmbeddingRequestInputsTypedDict
    r"""Text to embed."""


class EmbeddingRequest(BaseModel):
    model: str
    r"""ID of the model to use."""

    inputs: Annotated[EmbeddingRequestInputs, pydantic.Field(alias="input")]
    r"""Text to embed."""
