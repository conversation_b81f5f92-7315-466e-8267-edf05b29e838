narwhals-1.38.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-1.38.0.dist-info/METADATA,sha256=H3_qyJAt8x7RAcrFJBCMrSnwDRHdLwn0LJvQ-iOORGo,9323
narwhals-1.38.0.dist-info/RECORD,,
narwhals-1.38.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-1.38.0.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=iQ2cQSxj0se8nqgDgoqvzQNvCrDMaRKig_WQ8Vo6XyE,4518
narwhals/__pycache__/__init__.cpython-312.pyc,,
narwhals/__pycache__/_duration.cpython-312.pyc,,
narwhals/__pycache__/_enum.cpython-312.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-312.pyc,,
narwhals/__pycache__/_namespace.cpython-312.pyc,,
narwhals/__pycache__/_translate.cpython-312.pyc,,
narwhals/__pycache__/dataframe.cpython-312.pyc,,
narwhals/__pycache__/dependencies.cpython-312.pyc,,
narwhals/__pycache__/dtypes.cpython-312.pyc,,
narwhals/__pycache__/exceptions.cpython-312.pyc,,
narwhals/__pycache__/expr.cpython-312.pyc,,
narwhals/__pycache__/expr_cat.cpython-312.pyc,,
narwhals/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/__pycache__/expr_list.cpython-312.pyc,,
narwhals/__pycache__/expr_name.cpython-312.pyc,,
narwhals/__pycache__/expr_str.cpython-312.pyc,,
narwhals/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/__pycache__/functions.cpython-312.pyc,,
narwhals/__pycache__/group_by.cpython-312.pyc,,
narwhals/__pycache__/schema.cpython-312.pyc,,
narwhals/__pycache__/selectors.cpython-312.pyc,,
narwhals/__pycache__/series.cpython-312.pyc,,
narwhals/__pycache__/series_cat.cpython-312.pyc,,
narwhals/__pycache__/series_dt.cpython-312.pyc,,
narwhals/__pycache__/series_list.cpython-312.pyc,,
narwhals/__pycache__/series_str.cpython-312.pyc,,
narwhals/__pycache__/series_struct.cpython-312.pyc,,
narwhals/__pycache__/this.cpython-312.pyc,,
narwhals/__pycache__/translate.cpython-312.pyc,,
narwhals/__pycache__/typing.cpython-312.pyc,,
narwhals/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-312.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-312.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-312.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-312.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-312.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/dataframe.py,sha256=eZjs1Jax3l-DvVBRh3xqsnWCNcCHvp1FYJHbY8Mt8Cw,28929
narwhals/_arrow/expr.py,sha256=MtO33Hsu8vzIt-hrVKSo6Q-AnVfcCYh-WmPCl4PRLCc,8350
narwhals/_arrow/group_by.py,sha256=bUfjA98NmULkRsf2AogFHJBooINupXgXyJfpMUXyRSw,6686
narwhals/_arrow/namespace.py,sha256=5hVGYj2vLLs-TM5W5hjcjTfAZKC_IUbRX7Vo4mlaTrE,11330
narwhals/_arrow/selectors.py,sha256=-h9N7KHUSMa1LuVtBDgaXWFrgw1konyYFqEKioXnuJw,1042
narwhals/_arrow/series.py,sha256=yYppYLddImwtnxhKQ7FmGyZEy2LaTM5_t5YC4piz5-Q,44452
narwhals/_arrow/series_cat.py,sha256=vvNlPaHHcA-ORzh_79-oY03wt6aIg1rLI0At8FXr2Ok,598
narwhals/_arrow/series_dt.py,sha256=49HWgA_AvkTGQI8v4ofWB2Oin5Lu6b3UkigRgk4HjU4,7863
narwhals/_arrow/series_list.py,sha256=EpSul8DmTjQW00NQ5nLn9ZBSSUR0uuZ0IK6TLX1utwI,421
narwhals/_arrow/series_str.py,sha256=1QW4AxAMaFGBVI7lquOiGZXdLbDISN9B2FA-mgKdKF4,2554
narwhals/_arrow/series_struct.py,sha256=85pQSUqOdeMyjsnjaSr_4YBC2HRGD-dsnNy2tPveJRM,410
narwhals/_arrow/typing.py,sha256=PIAb-ULA3t5Z8KAoQjUDSyb1V5FWO3djTlTMdveDzFc,2433
narwhals/_arrow/utils.py,sha256=ZjS1NXtjCq4I5rN8DIkX8XhlkIHuh6j4h2NkH_WSKSo,18034
narwhals/_compliant/__init__.py,sha256=D0E51BKhyi2q9kAabmHKRVdLAxMDgUHCZwFvT61fdzU,2745
narwhals/_compliant/__pycache__/__init__.cpython-312.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-312.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-312.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-312.pyc,,
narwhals/_compliant/__pycache__/series.cpython-312.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-312.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-312.pyc,,
narwhals/_compliant/any_namespace.py,sha256=Wxkg_Za2ycC__fkWx9QUB8i1ZEgts1v3LcEYWSReDaE,3327
narwhals/_compliant/dataframe.py,sha256=ipEB33wRr1JSS_VZ_tjRcIC6o77GeFa3hFywv6NHWtk,17815
narwhals/_compliant/expr.py,sha256=gntc2ynK6MBQURPpT1FeKW7mlEBwQMzS2wc-MXOoRME,39768
narwhals/_compliant/group_by.py,sha256=yKDQ4xIgxY2jcWGlWzZQ7LvaImNKFUudZEVpaKNWcmI,8936
narwhals/_compliant/namespace.py,sha256=hp11B1vYbMyaRBqHSBxG9C5OaUB0B8cA1Q7itYt5vcU,7993
narwhals/_compliant/selectors.py,sha256=DNTN5ORGo9WJcVvbvFpboC4ZZn3xHGTZorz0A7phr4g,12721
narwhals/_compliant/series.py,sha256=pqtcDmsYC4A-CjNEDCk0K6B6MQnw38yNC54z5X-nG-w,14417
narwhals/_compliant/typing.py,sha256=gFuQCgj2iX1L0O4zgwk5wh0eITGyK8oxFaqKfPrHPi4,5692
narwhals/_compliant/when_then.py,sha256=i73wicaXGwnV5LVKVlaDjZzbmAMBpi0hofllLv07R00,6265
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-312.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-312.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-312.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-312.pyc,,
narwhals/_dask/__pycache__/utils.cpython-312.pyc,,
narwhals/_dask/dataframe.py,sha256=iBuC__Zv04mVqTkKwOTkURM_-coXCSD8kCqwqO_4Hx8,16534
narwhals/_dask/expr.py,sha256=25A2qRxAtbQGi--13z3I_Y1GenmnNKn8nm8Edwrm2Gs,26548
narwhals/_dask/expr_dt.py,sha256=Vz3InNLEN9RBW7WQF-Ogddli41jB5f_xNSX6TI2chjw,6568
narwhals/_dask/expr_str.py,sha256=rU-yY2oyUuIMl4BxeZZz0tpwoc7UGFIy_xlNwsIsjQA,3385
narwhals/_dask/group_by.py,sha256=RRD_mEoTJ2B-bRz85zIdECOTkMly0av-ji8VMNjy3N4,4353
narwhals/_dask/namespace.py,sha256=qyhMjMqpM2DHpF7cFD1MnN3jms2UiiM48o6yQ2AW9V0,12076
narwhals/_dask/selectors.py,sha256=37_48H9bl6kznhdqMw9ck7yvmmBi9ujaBGSn-8IA5ik,1015
narwhals/_dask/utils.py,sha256=-8Jh0DKVlD0kGIAarEtOIB1PqmzqhFbjizuGdF_0PBk,6805
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/typing.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-312.pyc,,
narwhals/_duckdb/dataframe.py,sha256=8ar_k8RIYOT8Ud3ee4nJFgAb5h5TsyuXJGhzXBEIa44,19973
narwhals/_duckdb/expr.py,sha256=G0sfw8BXxTdEj4FcVKD584aoYRXdw84XouOC1YFydbk,30822
narwhals/_duckdb/expr_dt.py,sha256=XUkOrjzmIxSwZ1DgBEPTN9Or0VwiL_stYhvKrmIOL5U,4504
narwhals/_duckdb/expr_list.py,sha256=F1a7beLsKkve-53UugCrmdSWhextHSInoXMVkL6trKM,452
narwhals/_duckdb/expr_str.py,sha256=JzENdhJCDHpP8ZxCVMeWsdu5htVhFtz4CSL0_WkdlwY,3837
narwhals/_duckdb/expr_struct.py,sha256=2RhsCOEQ4pv5SDuxBi8b3Tw7MWPeueaZ5dKv98o__bU,541
narwhals/_duckdb/group_by.py,sha256=AxLvTDPg0tTMFGMdyN_kaB4bXofwRu-oUvrR-j-9LuA,1108
narwhals/_duckdb/namespace.py,sha256=uwplqFrzIalW5gS6R4vYqxFuAgfjhXnvohn9SmKn_ms,8618
narwhals/_duckdb/selectors.py,sha256=Yzp2sslSH3SSyIwGpBg9-PWxCbfTmdBDDHeklWG8OdE,967
narwhals/_duckdb/series.py,sha256=m6xT_DcrBO_FBa_RNR0DyoeyS1-8VO6cVPFU_GklN-s,1321
narwhals/_duckdb/typing.py,sha256=ARFvdLw78qRHWiHJUVMa1DiVilIh0d2rHz-tDXTwvsE,306
narwhals/_duckdb/utils.py,sha256=3I0ze7sI_OjTSxN9qyvPiM7GVbKYK5TOvv_8XaiR0Dg,9577
narwhals/_duration.py,sha256=AQo-sXAqbRe36l19AXX5arIg3GUeqJRjm0x10WDc3P8,2062
narwhals/_enum.py,sha256=sUR-04yIwjAMsX5eelKnc1UKXc5dBoj1do0krubAE04,1192
narwhals/_expression_parsing.py,sha256=KB4_LhUVlxQ9O1D30Kk7TF3T2UbYrVxnaqXLL75BDJA,17600
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-312.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_ibis/__pycache__/series.cpython-312.pyc,,
narwhals/_ibis/dataframe.py,sha256=S1MdPv0aUmyNp_Vy0A4YU0bEN9STwHmxGsd9lCei124,5429
narwhals/_ibis/series.py,sha256=aZE5RUtK8I6YuzhKXfVSygELYH87njuZGdbGfkz8iX8,1220
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-312.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_interchange/__pycache__/series.cpython-312.pyc,,
narwhals/_interchange/dataframe.py,sha256=OQKXuaaqd_U7toWSdOOxOITkGGGcfyLaFN57zcsNVbE,6550
narwhals/_interchange/series.py,sha256=jsULNMhw1ShCmw_h_BF8xDGa5b_HnOtqHVGsJ8AnUkI,1750
narwhals/_namespace.py,sha256=GKovft2BX_QYy8nt9te3SOykSm6SPo-B15PtUd9W6R8,13026
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=t2xANJF9ftLUaLNP-AV3z9Qs9TCa-UqNJiDaYXRHedE,39917
narwhals/_pandas_like/expr.py,sha256=Uj_p4Lzg4--MBzsZu7ju03qRMmKHwvGcXzBg93JftY8,15641
narwhals/_pandas_like/group_by.py,sha256=AYJg6eGKijBTPcW6OLzsy8jy-HqL8iZtVdAhkBD2jh0,13562
narwhals/_pandas_like/namespace.py,sha256=N9KMDhptLohDyKTiXcU5LHALBtVpNFfAGqjuk-1Q2eA,13146
narwhals/_pandas_like/selectors.py,sha256=5yjR5IfBdBwAvdav4Kay15wBwbrjagA8YDeqvMvEJMo,1175
narwhals/_pandas_like/series.py,sha256=HXhxyuFykAXVf6fO9k2mVzyR88uTZf3GFr5MYkzvWpE,39406
narwhals/_pandas_like/series_cat.py,sha256=MJwCnJ49hfnODh6JgMHOCQ2KBlTbmySU6_X4XWaqiz4,527
narwhals/_pandas_like/series_dt.py,sha256=UhIZAx9I7yXGwJMc_ctY_X2X3Ci4F8sfLqzHJ_858kw,9833
narwhals/_pandas_like/series_list.py,sha256=MArjIXta2HiFR7JpI6Ld5Tdt1z52Z1lqS5yUUKEZIGk,1333
narwhals/_pandas_like/series_str.py,sha256=Wjn1C2XRgNgcujvk5f9AwEf4aJQ8clbA2MglTfGYbtA,3391
narwhals/_pandas_like/series_struct.py,sha256=vX9HoO42vHackvVozUfp8odM9uJ4owct49ydKDnohdk,518
narwhals/_pandas_like/typing.py,sha256=8L7LfKF777SuSgMARAB-VexDTGrvDUn4bioynyrwtCA,518
narwhals/_pandas_like/utils.py,sha256=-MqdW5BNXmYPXvMQkrSFPXL9Rl4id4JZDIl5zbgVLXA,28232
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-312.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_polars/__pycache__/expr.cpython-312.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-312.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-312.pyc,,
narwhals/_polars/__pycache__/series.cpython-312.pyc,,
narwhals/_polars/__pycache__/typing.cpython-312.pyc,,
narwhals/_polars/__pycache__/utils.cpython-312.pyc,,
narwhals/_polars/dataframe.py,sha256=ldQ_n7SREsXRV2ziU9RnbhnsknCJe_uqL8EUY1wIyoI,26561
narwhals/_polars/expr.py,sha256=SK3flVjBqDzQ6EW4xJ9Q097dDsrvBqXhnoopAKXOCNE,14525
narwhals/_polars/group_by.py,sha256=BbU3qhzTHIwjApnd0AQYaEHPRqrmnlz7OeaosXc_SGg,2554
narwhals/_polars/namespace.py,sha256=xb5S_sTqyeBWiistREXjPclcb2CUSE5gtZYF1fNi3uQ,11578
narwhals/_polars/series.py,sha256=oIIkfvDxbzdkR2k7m3Xxy7IxDVCRPMKvVAfmefeKGk0,26043
narwhals/_polars/typing.py,sha256=tsSQrfrC9k046fWQnPr3ACJOa7WUN8R8T-sNtrd56d4,702
narwhals/_polars/utils.py,sha256=chkx7WxYCKJ671_E2npwOTZzo-SLbdz2oCwoAVAI-Hk,8966
narwhals/_spark_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_spark_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_spark_like/dataframe.py,sha256=UhW7FA0YZ_CMcu73MXXIPGXcz5Ff6fg5XTQhMrlVGSg,20520
narwhals/_spark_like/expr.py,sha256=vtui8y_2ny_6QniK5GgG6Q3Q6Csai7qkFy_TIWRab-8,29898
narwhals/_spark_like/expr_dt.py,sha256=aXQvlSbWTl5JkmfKxjFD88evzgAQNVEQdx-L7BUW5QQ,2955
narwhals/_spark_like/expr_list.py,sha256=y3ZKxxoLz0_JAeGNi6ATSbrG5jeulThQmrk1AQP85mg,393
narwhals/_spark_like/expr_str.py,sha256=YK9xmyvy_87FdjZEIqyocNZwDvGkqPyMzFwjhkFxaTg,5680
narwhals/_spark_like/expr_struct.py,sha256=07kjoKdGpkLuynJ-TPNgr6MmojBD-hAHF5hmkOb57fo,521
narwhals/_spark_like/group_by.py,sha256=FoJHx9rMmamrx0EnGBYTuUuvVL788ivcLZWutgbP9M0,1231
narwhals/_spark_like/namespace.py,sha256=26CZKnTKH-0stNjE4V1pbgxRrFFLDGVOUoAxWZJxUt0,10701
narwhals/_spark_like/selectors.py,sha256=fnj_AjxIBotnZ7SyyeVT_Zp9ayq0Zajb5Jm9aarWeu8,1049
narwhals/_spark_like/typing.py,sha256=di49Xy-MuPObGoQqROLxZ2LrvPk07yoEl4T_8Rp27Lk,325
narwhals/_spark_like/utils.py,sha256=T1XR9TmmwjPz2qNJcU44OjLD_mymagDpO0maIcmeHEc,9882
narwhals/_translate.py,sha256=4P_24mwArNIyvkKDmRED4aCUx4NN5o1MAM4mwOSzGnQ,6864
narwhals/dataframe.py,sha256=QHXtOHZatTN9E13Mp48mXenkKs0BDzXQm6u6om0iUXU,127117
narwhals/dependencies.py,sha256=PKK1nMx0wqaIFUTFqfqzAN6alZrA9kIG80Nt9-fip7g,15402
narwhals/dtypes.py,sha256=3u1_te9V1nBUyxnYRIWR3l6kXZ6GkbQQAxWnUdyHRfY,23486
narwhals/exceptions.py,sha256=Vy44sE8jzL1fSLFGkSomMVcEPbgGkp_mQ29ECEQF-ZI,4328
narwhals/expr.py,sha256=kkcHhsfPzUy5M0QECHLqvAnGeG9hmFJEcgn9P6RL2Ao,104780
narwhals/expr_cat.py,sha256=xfZZ7wo0zr-J7PVmwGamPEQoPgapBKwzfefZUNBi6vY,1291
narwhals/expr_dt.py,sha256=xjl0uwuRSG3bP5Kjou3B3FDF-3LfouhjeXRPPw3Cmrg,31211
narwhals/expr_list.py,sha256=rc0XtKjs9gnBR2G2GbTV3PyOC3Q9DbwsHYSfboCFx3Y,1802
narwhals/expr_name.py,sha256=FyWXZvM6wO1QXgRjax3bANgvYGPEjAq3sRP3OcDNdPM,6030
narwhals/expr_str.py,sha256=2mMikWC5HRdsoV5v58yL8Qq5ogyh-o7ro2n8hulskRM,17733
narwhals/expr_struct.py,sha256=nnqrOXjT7ByndHBWXzWIQv3aqEywPO45sOyiTJ3-TbQ,1952
narwhals/functions.py,sha256=5PW8ou7qfLUhd86jAFEG0N5c9WmeED07lpZa720LiWk,67956
narwhals/group_by.py,sha256=-I7M386N74phaAC050rmTF_md1CwZ2EE2k7V-RGKEGg,7359
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=Ndo71SFb4RePXzCtw9oTWniKgDcdwH1jAAc29JHtU6w,6737
narwhals/selectors.py,sha256=1nNSxlAnmsx_NP8k7tCGuVdWgOWKD_H4RJVTzrIKvh0,10919
narwhals/series.py,sha256=7COG3q1uUYg9-tEMtkn2vDpDtga4BzxRNXKFYyY2VZQ,87223
narwhals/series_cat.py,sha256=bQqTMnE3PkzTeCmb3-cj-KKOrwPYlYIZhuQ4R4QDGiE,1066
narwhals/series_dt.py,sha256=nC8Y1KuD91S0NNIznCM4EUFU5rdxKEpvSw5Yj_Xd3P8,24244
narwhals/series_list.py,sha256=7aWkuywdT7UQRavY_FzL4-OdPaBISuRjqq2hRsAiXYo,1197
narwhals/series_str.py,sha256=0nB2QZS7_YCezgtR_KDrAu-gq0iwQKV5CrT-UJZu7q8,14754
narwhals/series_struct.py,sha256=Z45lh44NDD7QzqZrkVldW6ofqJXpObZHC3M0WSbB9Tc,1200
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__init__.py,sha256=hjYPiW-uCrjaYU72VhPQr8hB0hroMgacqPfU5E8ZiiM,62918
narwhals/stable/v1/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=-heA_lptD-9sTiH0j1UsXvERgj4MwS1dzS6BwyzB4js,3472
narwhals/stable/v1/_namespace.py,sha256=nsv44xpKnOO5E6IKKOJDuofqrif_IjRLEifBF25KfbM,295
narwhals/stable/v1/dependencies.py,sha256=PqYYOP_8w0ZJraajFnpYwc_ZdURRQIcgqplKsnymL_U,2204
narwhals/stable/v1/dtypes.py,sha256=C20rFSp455WJtGfaCZzxtUg600fPVZzIh9iAaz7bc40,2229
narwhals/stable/v1/selectors.py,sha256=4qDY5ZC6NyavSG6tK2M7kC1jOPSXJOltNzdgEx_Gu2Y,485
narwhals/stable/v1/typing.py,sha256=WkQc0LavXsuMaThJjRENA2YGu3W3RFEin9yuMk9kcM0,7054
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=mgnSXsviav2Fo6Bun8s7fP-rGcMKMp-kRRTYFVYYXnQ,29379
narwhals/typing.py,sha256=rKCiUu6ZlTJgOPXzwVYLpA1qoKHeHseqExh2J3IIYwY,14024
narwhals/utils.py,sha256=WMNn_5v0p-rzrVPcR3rWJx8EfyAgDlxb8-Z_cGkR-mk,65447
