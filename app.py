import concurrent.futures
import streamlit as st
import pandas as pd
import tempfile
import os
import zipfile
import hashlib
from ocr import ocr_mistral
from extraction import extraer_campos_llm, ensamblar_por_colaborador, consolidar_empleados

# Función para procesar un solo documento
def procesar_documento(file_path, file_name, api_key):
    try:
        texto = ocr_mistral(file_path, api_key=api_key)
        nombre = file_name.lower()
        # Detectar tipo de documento
        if "acta" in nombre:
            tipo = "acta"
        elif "nss" in nombre:
            tipo = "nss"
        elif "aviso" in nombre:
            tipo = "aviso_retencion"
        elif "comprobante" in nombre:
            tipo = "comprobante_domicilio"
        elif "sat" in nombre:
            tipo = "sat"
        elif "ine" in nombre:
            tipo = "ine"
        elif "cuenta" in nombre:
            tipo = "cuenta"
        else:
            tipo = "otro"
        resultado = extraer_campos_llm(texto, tipo, api_key=api_key)
        return {
            "archivo": file_name,
            "tipo": tipo,
            "id_colaborador": resultado.get("id_colaborador", ""),
            "campos": resultado.get("campos", {})
        }
    except Exception as e:
        print(f"❌ Error procesando {file_name}: {e}")
        return None

# Función para procesar un documento con caché
@st.cache_data
def procesar_documento_con_cache(file_content, file_name, api_key):
    """
    Procesa un documento con caché para evitar reprocesamiento.
    """
    # Generar hash del contenido del archivo para usarlo como clave de caché
    file_hash = hashlib.md5(file_content).hexdigest()
    
    # Guardar archivo temporal
    with tempfile.NamedTemporaryFile(delete=False, suffix="." + file_name.split(".")[-1]) as tmp:
        tmp.write(file_content)
        tmp_path = tmp.name
    
    try:
        # Procesar el documento
        resultado = procesar_documento(tmp_path, file_name, api_key)
        return resultado
    finally:
        # Limpiar archivo temporal
        if os.path.exists(tmp_path):
            os.remove(tmp_path)

st.set_page_config(page_title="Desempapelador - Expediente RH", layout="wide")

# --- SIDEBAR: Branding, copy, instrucciones, API Key ---
with st.sidebar:
    st.image("https://cdn-icons-png.flaticon.com/512/2991/2991108.png", width=80)
    st.title("🧾 Desempapelador")
    st.markdown("""
    Convierte papeles en datos útiles. Sin llorar.

    Sube los documentos del expediente laboral de un empleado (PDF o imagen) y esta app los desmenuza con IA + OCR.
    Te entrega todo en una tabla editable, lista para exportar a Excel.

    ---
    ### 🔒 Privacidad
    - Tus archivos no se guardan.
    - Solo tú y Mixtral los ven.
    - Los datos se eliminan tras procesar.

    ---
    """)
    # Aviso de privacidad en expander (compatible con todas las versiones)
    with st.expander("🔒 Aviso de Privacidad"):
        st.markdown("""
        ### Aviso de Privacidad

        En **Desempapelador RH** la privacidad y seguridad de tu información es una prioridad.

        - **Tus archivos solo existen durante tu sesión.**  
          Los documentos que subes se procesan de forma temporal y se eliminan automáticamente después de usarse. No se guardan en ningún servidor ni base de datos.

        - **Nadie más puede ver tus archivos ni tus resultados.**  
          Cada usuario tiene su propia sesión aislada. Otros usuarios no pueden acceder a tus documentos ni a tus datos, aunque estén usando la app al mismo tiempo.

        - **Tu API Key de Mistral es privada.**  
          La clave que ingresas solo se utiliza en tu sesión y nunca se almacena ni se comparte.

        - **El código fuente está en un repositorio privado.**  
          Solo el responsable de la app tiene acceso al código y a la configuración.

        - **Solo el proveedor de la nube (Streamlit Cloud) podría, en teoría, acceder a los archivos temporales mientras existen.**  
          Esto es una limitación técnica de cualquier servicio en la nube, pero se confía en las buenas prácticas de privacidad del proveedor.

        **Si tienes dudas sobre la privacidad de tus datos, puedes contactarme directamente:**  
        📧 <EMAIL>
        """)
    st.title("MixtralOCR")
    st.markdown("### 🔑 API Key")
    
    # API Key de Mistral
    api_key_usuario = st.text_input(
        "Ingresa tu API Key de Mistral:",
        type="password",
        help="Tu API Key se usa solo en esta sesión y no se almacena."
    )
    
    st.markdown("### ⚙️ Configuración")
    
    # Opción para elegir entre velocidad y precisión
    modo_procesamiento = st.radio(
        "Modo de procesamiento:",
        ["Rápido (menos preciso)", "Balanceado", "Preciso (más lento)"],
        index=1
    )
    st.session_state['modo_procesamiento'] = modo_procesamiento
    
    # Opción para usar o no Mistral OCR
    usar_mistral_ocr = st.checkbox("Usar Mistral OCR (más preciso pero más lento)", value=True)
    st.session_state['usar_mistral_ocr'] = usar_mistral_ocr
    
    # Opciones avanzadas
    with st.expander("Opciones avanzadas"):
        max_workers = st.slider("Procesamiento paralelo (hilos)", 1, 8, 4)
        st.session_state['max_workers'] = max_workers
        
        limite_caracteres = st.slider("Límite de caracteres para LLM", 1000, 8000, 4000)
        st.session_state['limite_caracteres'] = limite_caracteres

# --- MAIN: Zona central de acción ---
st.markdown("<h1 style='text-align: center;'>Desempapelador RH</h1>", unsafe_allow_html=True)
st.markdown("<h3 style='text-align: center; color: #4F8BF9;'>Automatiza la captura de expedientes laborales</h3>", unsafe_allow_html=True)
st.write("")

# Recomendación sobre el nombre de los archivos
st.info("""
**Recomendación:** Para facilitar la identificación automática, nombra tus archivos así:

- Usa un **ID de empleado corto y único** al inicio del nombre del archivo (ejemplo: 12345, abc01, etc.)
- Luego, agrega el tipo de documento (acta, nss, curp, ine, etc.)
- Ejemplo: `12345_acta.pdf`, `12345_nss.pdf`, `abc01_ine.jpg`
- También puedes subir un **archivo ZIP** que contenga múltiples documentos de un empleado o varios empleados

Esto ayuda a agrupar y vincular los documentos correctamente.
""")

# Zona de carga de archivos, centrada
st.markdown("### Sube tus archivos PDF o imagen")
archivos_subidos = st.file_uploader(
    "Arrastra aquí uno o más archivos (PDF, JPG, PNG, ZIP)", 
    type=["pdf", "jpg", "jpeg", "png", "zip"], 
    accept_multiple_files=True, 
    label_visibility="collapsed"
)

# Botón de procesar, centrado y grande
col1, col2, col3 = st.columns([2,2,2])
with col2:
    procesar = st.button("🚀 Procesar documentos", use_container_width=True, disabled=not (archivos_subidos and api_key_usuario))

# Procesamiento y resultados
if archivos_subidos and procesar and api_key_usuario:
    resultados = []
    archivos_a_procesar = []
    
    # Preparar lista de archivos a procesar
    with st.spinner("Preparando archivos..."):
        with tempfile.TemporaryDirectory() as temp_dir:
            # Procesar archivos ZIP y normales
            for archivo in archivos_subidos:
                if archivo.name.lower().endswith('.zip'):
                    # Guardar el ZIP en un archivo temporal
                    zip_path = os.path.join(temp_dir, archivo.name)
                    with open(zip_path, 'wb') as f:
                        f.write(archivo.getvalue())
                    
                    # Extraer contenido del ZIP
                    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                        zip_ref.extractall(os.path.join(temp_dir, "zip_extracted"))
                    
                    # Añadir archivos extraídos a la lista
                    for root, _, files in os.walk(os.path.join(temp_dir, "zip_extracted")):
                        for file in files:
                            if file.lower().endswith(('.pdf', '.jpg', '.jpeg', '.png')):
                                file_path = os.path.join(root, file)
                                archivos_a_procesar.append((file_path, file))
                else:
                    # Guardar archivo individual
                    file_path = os.path.join(temp_dir, archivo.name)
                    with open(file_path, 'wb') as f:
                        f.write(archivo.getvalue())
                    archivos_a_procesar.append((file_path, archivo.name))
            
            # Configurar número de trabajadores según la opción del usuario
            max_workers = st.session_state.get('max_workers', 4)
            
            # Mostrar barra de progreso
            progress_bar = st.progress(0)
            status_text = st.empty()
            total_archivos = len(archivos_a_procesar)
            
            # Función para procesar un documento
            def procesar_doc(args):
                file_path, file_name = args
                try:
                    # Obtener modo de procesamiento
                    modo = "balanceado"
                    if st.session_state.get('modo_procesamiento') == "Rápido (menos preciso)":
                        modo = "rapido"
                    elif st.session_state.get('modo_procesamiento') == "Preciso (más lento)":
                        modo = "preciso"
                    
                    # Usar Mistral OCR según configuración
                    usar_mistral = st.session_state.get('usar_mistral_ocr', True)
                    
                    # Extraer texto con OCR
                    texto = ocr_mistral(file_path, api_key=api_key_usuario, 
                                       usar_mistral=usar_mistral, modo=modo)
                    
                    # Detectar tipo de documento
                    tipo = detectar_tipo_documento(file_name)
                    
                    # Extraer campos
                    limite = st.session_state.get('limite_caracteres', 4000)
                    resultado = extraer_campos_llm(texto[:limite], tipo, api_key=api_key_usuario)
                    
                    return {
                        "archivo": file_name,
                        "tipo": tipo,
                        "id_colaborador": resultado.get("id_colaborador", ""),
                        "campos": resultado.get("campos", {})
                    }
                except Exception as e:
                    print(f"❌ Error procesando {file_name}: {e}")
                    return None
            
            # Procesar archivos en paralelo
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                for i, resultado in enumerate(executor.map(procesar_doc, archivos_a_procesar)):
                    if resultado:
                        resultados.append(resultado)
                    
                    # Actualizar barra de progreso
                    progress_bar.progress((i + 1) / total_archivos)
                    status_text.text(f"Procesando archivo {i+1} de {total_archivos}: {archivos_a_procesar[i][1]}")
    
    # Continuar con el procesamiento de resultados
    expedientes = ensamblar_por_colaborador(resultados)
    df_empleados = consolidar_empleados(expedientes)
    st.success("¡Procesamiento completado!")
    st.markdown("### Resultados extraídos (puedes revisar y corregir antes de descargar):")
    df_edit = st.data_editor(df_empleados, num_rows="dynamic", use_container_width=True, key="edicion")
    # Descargar Excel
    excel_tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
    df_edit.to_excel(excel_tmp.name, index=False)
    excel_tmp.close()
    with open(excel_tmp.name, "rb") as f:
        st.download_button(
            label="⬇️ Descargar Excel consolidado",
            data=f,
            file_name="expedientes_consolidados.xlsx",
            mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    os.remove(excel_tmp.name)
elif procesar and not api_key_usuario:
    st.error("Debes ingresar tu API Key de Mistral para procesar los documentos.")

# Pie de página
st.markdown("---")
st.markdown("<div style='text-align: center; color: gray;'>Hecho con ❤️ por Efrén · ¿Te ahorró tiempo? Invítame una cerveza en <a href='https://ko-fi.com/nominante' target='_blank'>ko-fi.com/nominante</a></div>", unsafe_allow_html=True)

def detectar_tipo_documento(nombre_archivo):
    """
    Detecta el tipo de documento basado en el nombre del archivo
    y palabras clave comunes en cada tipo de documento.
    """
    nombre = nombre_archivo.lower()
    
    # Mapeo de palabras clave a tipos de documento
    tipos_documentos = {
        "acta": ["acta", "nacimiento", "birth", "nac"],
        "nss": ["nss", "seguro", "social", "imss"],
        "aviso_retencion": ["aviso", "retencion", "retención", "infonavit", "credito"],
        "comprobante_domicilio": ["comprobante", "domicilio", "cfe", "telmex", "agua", "gas", "address", "luz", "predial"],
        "sat": ["sat", "rfc", "fiscal", "contribuyente", "tax", "hacienda", "constancia"],
        "ine": ["ine", "electoral", "credencial", "voter", "ife", "identificacion"],
        "cuenta": ["cuenta", "bancaria", "banco", "bank", "account", "clabe", "nomina", "nómina"]
    }
    
    # Buscar coincidencias
    for tipo, palabras_clave in tipos_documentos.items():
        for palabra in palabras_clave:
            if palabra in nombre:
                return tipo
    
    # Si no hay coincidencias, intentar detectar por ID de empleado
    partes = nombre.split('_')
    if len(partes) > 1:
        segunda_parte = partes[1].split('.')[0]  # Quitar extensión
        for tipo, palabras_clave in tipos_documentos.items():
            for palabra in palabras_clave:
                if palabra in segunda_parte:
                    return tipo
    
    return "otro"
