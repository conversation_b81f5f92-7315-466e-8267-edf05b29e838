"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .apiendpoint import APIEndpoint
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import validate_open_enum
from pydantic import model_serializer
from pydantic.functional_validators import PlainValidator
from typing import Dict, List, Optional
from typing_extensions import Annotated, NotRequired, TypedDict


class BatchJobInTypedDict(TypedDict):
    input_files: List[str]
    endpoint: APIEndpoint
    model: str
    metadata: NotRequired[Nullable[Dict[str, str]]]
    timeout_hours: NotRequired[int]


class BatchJobIn(BaseModel):
    input_files: List[str]

    endpoint: Annotated[APIEndpoint, PlainValidator(validate_open_enum(False))]

    model: str

    metadata: OptionalNullable[Dict[str, str]] = UNSET

    timeout_hours: Optional[int] = 24

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["metadata", "timeout_hours"]
        nullable_fields = ["metadata"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
