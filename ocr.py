import os
import json
import fitz  # PyMuPDF
from mistralai import Mi<PERSON><PERSON>
from dotenv import load_dotenv
from PIL import Image
import tempfile

def cargar_api_key():
    """
    Carga la API Key de Mistral desde las variables de entorno.
    Lanza un error si no está definida.
    Returns:
        str: API Key de Mistral
    """
    load_dotenv()
    api_key = os.environ.get("MISTRAL_API_KEY")
    if not api_key:
        raise ValueError("Error: La variable de entorno MISTRAL_API_KEY no está definida.")
    return api_key

def convertir_imagen_a_pdf(path_imagen):
    """
    Convierte una imagen (JPG, PNG) a PDF temporal para procesar con OCR.
    Args:
        path_imagen (str): Ruta de la imagen.
    Returns:
        str: Ruta del PDF temporal generado.
    """
    with Image.open(path_imagen) as img:
        img = img.convert("RGB")
        temp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
        img.save(temp_pdf, format="PDF")
        temp_pdf.close()
        return temp_pdf.name

def extract_text_pymupdf(pdf_path):
    """
    Extrae texto de un PDF usando PyMuPDF (fallback si OCR falla).
    Args:
        pdf_path (str): Ruta del PDF.
    Returns:
        str: Texto extraído.
    """
    doc = fitz.open(pdf_path)
    extracted_text = "\n\n".join([page.get_text() for page in doc])
    return extracted_text.strip()

def ocr_mistral(document_path, api_key=None, usar_mistral=True, modo="balanceado"):
    """
    Realiza OCR sobre un documento usando Mistral OCR o PyMuPDF según configuración.
    Args:
        document_path (str): Ruta del archivo PDF o imagen.
        api_key (str, opcional): API Key de Mistral. Si no se pasa, se carga del entorno.
        usar_mistral (bool): Si es True, intenta usar Mistral OCR primero.
        modo (str): "rapido", "balanceado" o "preciso" para ajustar el comportamiento.
    Returns:
        str: Texto extraído del documento.
    """
    if api_key is None:
        api_key = cargar_api_key()
    client = Mistral(api_key=api_key)
    
    # Si es modo rápido, usar siempre PyMuPDF primero
    if modo == "rapido" or not usar_mistral:
        try:
            return extract_text_pymupdf(document_path)
        except Exception as e:
            print(f"⚠️ Error con PyMuPDF: {e}")
            # Solo si falla PyMuPDF y está habilitado Mistral, continuar con Mistral
            if not usar_mistral:
                return ""
    
    # Para modos balanceado y preciso, o si falló PyMuPDF en modo rápido
    try:
        # Subir el archivo y obtener la URL firmada
        with open(document_path, "rb") as f:
            uploaded_pdf = client.files.upload(
                file={
                    "file_name": os.path.basename(document_path),
                    "content": f,
                },
                purpose="ocr"
            )
        signed_url = client.files.get_signed_url(file_id=uploaded_pdf.id)
        
        # Procesar OCR con diferentes configuraciones según el modo
        ocr_params = {}
        if modo == "preciso":
            ocr_params = {"quality": "high"}
        
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "document_url",
                "document_url": signed_url.url
            },
            include_image_base64=False,
            **ocr_params
        )
        
        # Extraer texto de las páginas
        extracted_text = "\n\n".join(
            [page.markdown for page in ocr_response.pages if hasattr(page, "markdown") and page.markdown]
        )
        
        # Si no hay texto útil, usar PyMuPDF como fallback
        if not extracted_text.strip():
            print("⚠️ Mistral no devolvió texto útil, usando PyMuPDF como fallback...")
            return extract_text_pymupdf(document_path)
        
        return extracted_text
    except Exception as e:
        print(f"❌ Error durante el procesamiento OCR: {e}")
        # En caso de error, intentar con PyMuPDF
        try:
            return extract_text_pymupdf(document_path)
        except Exception as e2:
            print(f"❌ Error también con PyMuPDF: {e2}")
            return ""


# Este archivo está listo para importar la función ocr_mistral en otros módulos.

